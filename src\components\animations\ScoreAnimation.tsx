'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

interface ScoreAnimationProps {
  score: number;
  teamName: string;
  color: string;
  isActive?: boolean;
}

export const ScoreAnimation = ({ score, teamName, color, isActive = false }: ScoreAnimationProps) => {
  const [prevScore, setPrevScore] = useState(score);
  const [showIncrement, setShowIncrement] = useState(false);

  useEffect(() => {
    if (score > prevScore) {
      setShowIncrement(true);
      setTimeout(() => setShowIncrement(false), 2000);
    }
    setPrevScore(score);
  }, [score, prevScore]);

  return (
    <div className="relative">
      <motion.div
        className={`text-6xl font-bold ${color} ${isActive ? 'animate-pulse' : ''}`}
        animate={isActive ? { scale: [1, 1.1, 1] } : {}}
        transition={{ duration: 0.5 }}
      >
        {score}
      </motion.div>
      
      <AnimatePresence>
        {showIncrement && (
          <motion.div
            initial={{ opacity: 0, y: 0, scale: 1 }}
            animate={{ opacity: 1, y: -50, scale: 1.5 }}
            exit={{ opacity: 0, y: -100, scale: 0.5 }}
            transition={{ duration: 2 }}
            className={`absolute top-0 left-1/2 transform -translate-x-1/2 text-2xl font-bold ${color}`}
          >
            +1
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface BracketAnimationProps {
  teams: string[];
  winners: string[];
  currentRound: number;
}

export const BracketAnimation = ({ teams, winners, currentRound }: BracketAnimationProps) => {
  return (
    <div className="flex justify-center items-center space-x-8">
      {teams.map((team, index) => (
        <motion.div
          key={team}
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className={`p-4 rounded-lg ${
            winners.includes(team) 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-700 text-gray-300'
          }`}
        >
          <motion.div
            animate={winners.includes(team) ? { scale: [1, 1.05, 1] } : {}}
            transition={{ duration: 0.3 }}
          >
            {team}
          </motion.div>
        </motion.div>
      ))}
    </div>
  );
};

interface TournamentProgressProps {
  currentStage: string;
  totalStages: string[];
  progress: number;
}

export const TournamentProgress = ({ currentStage, totalStages, progress }: TournamentProgressProps) => {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="flex justify-between mb-2">
        {totalStages.map((stage, index) => (
          <motion.div
            key={stage}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`text-sm ${
              stage === currentStage 
                ? 'text-blue-400 font-semibold' 
                : 'text-gray-400'
            }`}
          >
            {stage}
          </motion.div>
        ))}
      </div>
      
      <div className="w-full bg-gray-700 rounded-full h-2">
        <motion.div
          className="bg-blue-500 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 1, ease: "easeInOut" }}
        />
      </div>
    </div>
  );
};

interface LiveIndicatorProps {
  isLive: boolean;
  viewerCount?: number;
}

export const LiveIndicator = ({ isLive, viewerCount }: LiveIndicatorProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="flex items-center space-x-2"
    >
      {isLive && (
        <>
          <motion.div
            className="w-3 h-3 bg-red-500 rounded-full"
            animate={{ opacity: [1, 0.3, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />
          <span className="text-red-400 font-semibold">LIVE</span>
        </>
      )}
      {viewerCount && (
        <motion.span
          className="text-gray-300"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {viewerCount.toLocaleString()} viewers
        </motion.span>
      )}
    </motion.div>
  );
};

interface ParticleEffectProps {
  trigger: boolean;
  color?: string;
}

export const ParticleEffect = ({ trigger, color = 'blue' }: ParticleEffectProps) => {
  const particles = Array.from({ length: 20 }, (_, i) => i);

  return (
    <AnimatePresence>
      {trigger && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {particles.map((particle) => (
            <motion.div
              key={particle}
              className={`absolute w-2 h-2 bg-${color}-400 rounded-full`}
              initial={{
                x: '50%',
                y: '50%',
                opacity: 1,
                scale: 0,
              }}
              animate={{
                x: `${50 + (Math.random() - 0.5) * 200}%`,
                y: `${50 + (Math.random() - 0.5) * 200}%`,
                opacity: 0,
                scale: [0, 1, 0],
              }}
              exit={{ opacity: 0 }}
              transition={{
                duration: 2,
                ease: "easeOut",
                delay: Math.random() * 0.5,
              }}
            />
          ))}
        </div>
      )}
    </AnimatePresence>
  );
};
