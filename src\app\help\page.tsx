'use client';

import { motion } from 'framer-motion';
import { 
  HelpCircle, 
  MessageCircle, 
  Phone, 
  Mail, 
  Clock, 
  MapPin,
  Gamepad2,
  Trophy,
  Users,
  Calendar,
  CreditCard,
  Shield
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Card, Button } from '@/components/ui';

const faqData = [
  {
    category: 'Tournament Registration',
    icon: Trophy,
    questions: [
      {
        q: 'How do I register for a FIFA 24 tournament?',
        a: 'Visit our registration page, select your desired tournament, fill out the form with your details, and submit. You\'ll receive a confirmation email with venue details and tournament information.'
      },
      {
        q: 'What do I need to bring to the tournament?',
        a: 'Just bring yourself and a valid ID! We provide all gaming equipment including PS5 consoles, controllers, and monitors. You may bring your own controller if preferred.'
      },
      {
        q: 'Can I register on the day of the tournament?',
        a: 'Registration closes 1 week before each tournament. We don\'t accept walk-in registrations to ensure proper planning and venue capacity management.'
      }
    ]
  },
  {
    category: 'Tournament Rules',
    icon: Shield,
    questions: [
      {
        q: 'What are the tournament rules?',
        a: 'All tournaments follow FIFA 24 official rules. Matches are played on Professional difficulty, 6-minute halves, with standard FIFA Ultimate Team or Seasons mode depending on the tournament.'
      },
      {
        q: 'What happens if I\'m late?',
        a: 'Players must arrive 30 minutes before their scheduled match time. Late arrivals may result in automatic forfeit. Contact us immediately if you\'re running late.'
      },
      {
        q: 'Are there age restrictions?',
        a: 'Players must be 16 years or older. Players under 18 need parental consent and emergency contact information.'
      }
    ]
  },
  {
    category: 'Venues & Locations',
    icon: MapPin,
    questions: [
      {
        q: 'Where are the tournaments held?',
        a: 'We host tournaments at premium gaming venues across Addis Ababa including Addis Gaming Arena (Bole Road), Merkato Gaming Hub, Piazza Gaming Center, and Kazanchis Pro Lounge.'
      },
      {
        q: 'Is parking available?',
        a: 'Yes, all our venues have parking facilities. Some venues offer free parking while others may charge a small fee. Check your tournament confirmation email for specific venue details.'
      },
      {
        q: 'Are the venues accessible?',
        a: 'All our partner venues are wheelchair accessible and have facilities for players with disabilities. Contact us in advance if you need special accommodations.'
      }
    ]
  },
  {
    category: 'Prizes & Payments',
    icon: CreditCard,
    questions: [
      {
        q: 'How are prizes distributed?',
        a: 'Cash prizes are awarded immediately after the tournament finals. Winners receive their prizes in Ethiopian Birr equivalent. We also provide certificates and trophies for top 3 finishers.'
      },
      {
        q: 'Is there a registration fee?',
        a: 'Most tournaments have a small registration fee (50-200 ETB) to cover venue costs and ensure serious participation. This is clearly stated during registration.'
      },
      {
        q: 'What if I need to cancel my registration?',
        a: 'Cancellations are accepted up to 48 hours before the tournament. Registration fees are refundable if cancelled within this timeframe.'
      }
    ]
  }
];

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <HelpCircle className="h-16 w-16 text-blue-400 mx-auto mb-4" />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Help Center
          </h1>
          <p className="text-xl text-gray-300">
            Find answers to common questions about our FIFA 24 tournaments in Ethiopia
          </p>
        </motion.div>

        {/* Contact Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="text-center p-6 bg-gradient-to-br from-blue-900/20 to-blue-800/20 border-blue-600/30">
              <Phone className="h-8 w-8 text-blue-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Phone Support</h3>
              <p className="text-gray-300 text-sm mb-3">Call us for immediate assistance</p>
              <p className="text-blue-400 font-semibold">+251-11-XXX-XXXX</p>
              <p className="text-gray-400 text-xs mt-1">Mon-Fri 9AM-6PM EAT</p>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="text-center p-6 bg-gradient-to-br from-green-900/20 to-green-800/20 border-green-600/30">
              <Mail className="h-8 w-8 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Email Support</h3>
              <p className="text-gray-300 text-sm mb-3">Send us your questions</p>
              <p className="text-green-400 font-semibold"><EMAIL></p>
              <p className="text-gray-400 text-xs mt-1">Response within 24 hours</p>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="text-center p-6 bg-gradient-to-br from-purple-900/20 to-purple-800/20 border-purple-600/30">
              <MessageCircle className="h-8 w-8 text-purple-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Live Chat</h3>
              <p className="text-gray-300 text-sm mb-3">Chat with our team</p>
              <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                Start Chat
              </Button>
              <p className="text-gray-400 text-xs mt-2">Available during business hours</p>
            </Card>
          </motion.div>
        </div>

        {/* FAQ Sections */}
        <div className="space-y-8">
          {faqData.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
            >
              <Card className="p-6">
                <div className="flex items-center mb-6">
                  <category.icon className="h-6 w-6 text-blue-400 mr-3" />
                  <h2 className="text-2xl font-bold text-white">{category.category}</h2>
                </div>
                
                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <div key={faqIndex} className="border-b border-gray-700 pb-4 last:border-b-0">
                      <h3 className="text-lg font-semibold text-white mb-2">{faq.q}</h3>
                      <p className="text-gray-300">{faq.a}</p>
                    </div>
                  ))}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Help */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-12 text-center"
        >
          <Card className="p-8 bg-gradient-to-r from-gray-800/50 to-gray-700/50">
            <h2 className="text-2xl font-bold text-white mb-4">Still Need Help?</h2>
            <p className="text-gray-300 mb-6">
              Can't find what you're looking for? Our support team is here to help you with any questions about FIFA 24 tournaments in Ethiopia.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">
                <MessageCircle className="mr-2 h-5 w-5" />
                Contact Support
              </Button>
              <Button variant="secondary" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                Schedule Call
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
