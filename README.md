# Gaming Tournament Platform

A comprehensive, modern gaming tournament management and streaming platform built with Next.js, Tailwind CSS, and Framer Motion. Features real-time tournament displays, professional admin dashboards, and seamless user registration systems.

## 🚀 Features

### Core Features
- **Modern Landing Page** - Stunning hero section with live tournament displays and animations
- **Tournament Management** - Complete tournament creation, management, and bracket systems
- **User Registration** - Streamlined competitor registration with form validation
- **Real-time Display** - Full-screen tournament display with live score updates
- **Admin Dashboards** - Separate dashboards for IT admins and tournament admins
- **Responsive Design** - Fully responsive across all devices

### Technical Features
- **Next.js 15** - Latest React framework with App Router
- **Tailwind CSS v4** - Modern utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions
- **TypeScript** - Full type safety
- **Component Library** - Reusable UI components
- **Real-time Updates** - Live score animations and bracket updates

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **UI Components**: Headless UI
- **Icons**: Lucide React
- **Language**: TypeScript
- **Package Manager**: npm

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gaming-tournament
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
gaming-tournament/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── admin/             # IT Admin dashboard
│   │   ├── tournament-admin/  # Tournament Admin dashboard
│   │   ├── tournaments/       # Tournament listing page
│   │   ├── register/          # Registration page
│   │   ├── display/           # Full-screen display
│   │   └── page.tsx           # Landing page
│   ├── components/
│   │   ├── ui/                # Reusable UI components
│   │   ├── layout/            # Layout components
│   │   └── animations/        # Animation components
│   └── lib/                   # Utility functions
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🎯 Pages Overview

### Landing Page (`/`)
- Hero section with animated elements
- Live tournament showcase
- Feature highlights
- Statistics display
- Call-to-action sections

### Tournaments (`/tournaments`)
- Tournament listing with filters
- Status badges (Live, Upcoming, Registration Open)
- Tournament details and registration links
- Responsive card layout

### Registration (`/register`)
- Multi-step registration form
- Form validation and error handling
- Team formation options
- Tournament information sidebar

### Display (`/display`)
- Full-screen tournament display
- Real-time score updates
- Animated score changes
- Tournament bracket visualization
- Live viewer count

### IT Admin Dashboard (`/admin`)
- System overview and statistics
- User management
- Content management
- Theme customization
- System monitoring
- Security settings

### Tournament Admin Dashboard (`/tournament-admin`)
- Tournament creation and management
- Live match control
- Bracket management
- Display settings control
- Real-time score updates

## 🎨 UI Components

### Core Components
- **Button** - Multiple variants with animations
- **Card** - Flexible container with hover effects
- **Input** - Form inputs with validation
- **Modal** - Accessible modal dialogs
- **Table** - Data tables with sorting
- **Badge** - Status indicators
- **Tabs** - Tabbed interfaces

### Animation Components
- **ScoreAnimation** - Animated score displays
- **BracketAnimation** - Tournament bracket animations
- **TournamentProgress** - Progress indicators
- **LiveIndicator** - Live status indicators
- **ParticleEffect** - Celebration effects

## 🔧 Development

### Available Scripts
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

### Code Style
- TypeScript for type safety
- ESLint for code quality
- Consistent component structure
- Utility-first CSS with Tailwind

## 🚀 Deployment

The application is ready for deployment on various platforms:

### Vercel (Recommended)
```bash
npm i -g vercel
vercel
```

### Netlify
Configure build settings:
- Build command: `npm run build`
- Publish directory: `out`

### Docker
```bash
docker build -t gaming-tournament .
docker run -p 3000:3000 gaming-tournament
```

See [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for detailed deployment instructions.

## 🔮 Future Enhancements (Supabase Integration)

The platform is designed for easy Supabase integration:

- **Authentication** - User login/registration
- **Real-time Database** - Live tournament data
- **User Management** - Role-based access control
- **File Storage** - Tournament images and assets
- **Real-time Subscriptions** - Live updates

See [SUPABASE_INTEGRATION_PLAN.md](./SUPABASE_INTEGRATION_PLAN.md) for detailed integration plans.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Tailwind CSS for the utility-first approach
- Framer Motion for smooth animations
- Lucide for beautiful icons
- Headless UI for accessible components
