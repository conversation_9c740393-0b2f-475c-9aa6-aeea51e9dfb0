'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Phone, Gamepad2, Trophy, Users, MapPin, Calendar, Clock } from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Button, Card, Input } from '@/components/ui';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    gamertag: '',
    tournament: '',
    experience: '',
    emergencyContact: '',
    emergencyPhone: '',
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.gamertag.trim()) newErrors.gamertag = 'Gamertag is required';
    if (!formData.tournament) newErrors.tournament = 'Please select a tournament';
    if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      console.log('Form submitted:', formData);
      // Handle form submission
    }
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center mb-6">
            <Gamepad2 className="h-16 w-16 text-green-400" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            FIFA 24 Tournament Registration
          </h1>
          <p className="text-xl text-gray-300 mb-4">
            Register for in-person FIFA 24 competitions on PS5 in Addis Ababa, Ethiopia
          </p>
          <div className="flex flex-wrap justify-center gap-3">
            <span className="bg-green-600/20 text-green-400 px-4 py-2 rounded-full text-sm font-semibold">
              <Gamepad2 className="h-4 w-4 inline mr-2" />
              PS5 Exclusive
            </span>
            <span className="bg-blue-600/20 text-blue-400 px-4 py-2 rounded-full text-sm font-semibold">
              <MapPin className="h-4 w-4 inline mr-2" />
              In-Person Only
            </span>
            <span className="bg-yellow-600/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-semibold">
              <Trophy className="h-4 w-4 inline mr-2" />
              Cash Prizes
            </span>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Registration Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card>
              <h2 className="text-2xl font-bold text-white mb-6">Player Information</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="First Name"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    error={errors.firstName}
                    placeholder="Enter your first name"
                  />
                  <Input
                    label="Last Name"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    error={errors.lastName}
                    placeholder="Enter your last name"
                  />
                </div>

                <Input
                  label="Email Address"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  error={errors.email}
                  placeholder="Enter your email address"
                />

                <Input
                  label="Phone Number (Optional)"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="Enter your phone number"
                />

                <Input
                  label="FIFA Gamertag/PSN ID"
                  name="gamertag"
                  value={formData.gamertag}
                  onChange={handleInputChange}
                  error={errors.gamertag}
                  placeholder="Enter your PSN ID or FIFA gamertag"
                />

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-300">
                    Tournament Selection
                  </label>
                  <select
                    name="tournament"
                    value={formData.tournament}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    aria-label="Select tournament"
                  >
                    <option value="">Select a tournament</option>
                    <option value="fifa-championship">FIFA 24 Championship Finals - $5,000 (Addis Gaming Arena)</option>
                    <option value="fifa-winter">FIFA Winter Cup - $2,500 (Merkato Gaming Hub)</option>
                    <option value="fifa-rookie">FIFA Rookie League - $1,500 (Piazza Gaming Center)</option>
                    <option value="fifa-masters">FIFA Masters Cup - $7,500 (Kazanchis Pro Lounge)</option>
                  </select>
                  {errors.tournament && <p className="text-sm text-red-400">{errors.tournament}</p>}
                </div>

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-300">
                    FIFA Experience Level
                  </label>
                  <select
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    aria-label="Select FIFA experience level"
                  >
                    <option value="">Select your FIFA skill level</option>
                    <option value="beginner">Beginner (Division 8-10)</option>
                    <option value="intermediate">Intermediate (Division 5-7)</option>
                    <option value="advanced">Advanced (Division 2-4)</option>
                    <option value="elite">Elite (Division 1 / Elite Division)</option>
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Emergency Contact Name"
                    name="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={handleInputChange}
                    placeholder="Full name of emergency contact"
                  />
                  <Input
                    label="Emergency Contact Phone"
                    name="emergencyPhone"
                    type="tel"
                    value={formData.emergencyPhone}
                    onChange={handleInputChange}
                    placeholder="Emergency contact phone number"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id="agreeToTerms"
                      name="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onChange={(e) => setFormData(prev => ({ ...prev, agreeToTerms: e.target.checked }))}
                      className="mt-1 h-4 w-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="agreeToTerms" className="text-sm text-gray-300">
                      I agree to the tournament rules, terms and conditions, and understand this is an in-person event.
                      I confirm I will be present at the specified venue on the tournament date.
                    </label>
                  </div>
                  {errors.agreeToTerms && <p className="text-sm text-red-400">{errors.agreeToTerms}</p>}
                </div>

                <Button type="submit" size="lg" className="w-full">
                  <Trophy className="mr-2 h-5 w-5" />
                  Register for Tournament
                </Button>
              </form>
            </Card>
          </motion.div>

          {/* Tournament Info Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            <Card>
              <h3 className="text-xl font-bold text-white mb-4">FIFA 24 Tournament Info</h3>
              <div className="space-y-3">
                <div className="flex items-center text-gray-300">
                  <Trophy className="h-4 w-4 mr-2 text-yellow-400" />
                  <span>Up to $7,500 Prize Pool</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Users className="h-4 w-4 mr-2 text-blue-400" />
                  <span>16-48 Players per Tournament</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Gamepad2 className="h-4 w-4 mr-2 text-green-400" />
                  <span>PS5 Exclusive</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <MapPin className="h-4 w-4 mr-2 text-red-400" />
                  <span>Premium Gaming Venues</span>
                </div>
              </div>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-white mb-4">What to Expect</h3>
              <div className="space-y-2 text-gray-300 text-sm">
                <div className="flex items-start space-x-2">
                  <Calendar className="h-4 w-4 mr-1 text-blue-400 mt-0.5" />
                  <span>Registration closes 1 week before tournament</span>
                </div>
                <div className="flex items-start space-x-2">
                  <Clock className="h-4 w-4 mr-1 text-green-400 mt-0.5" />
                  <span>Tournaments run 4-8 hours depending on size</span>
                </div>
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 mr-1 text-red-400 mt-0.5" />
                  <span>Must be present at venue on tournament day</span>
                </div>
                <div className="flex items-start space-x-2">
                  <Trophy className="h-4 w-4 mr-1 text-yellow-400 mt-0.5" />
                  <span>Cash prizes awarded immediately after finals</span>
                </div>
              </div>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-white mb-4">Tournament Venues in Addis Ababa</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <MapPin className="h-4 w-4 text-red-400" />
                    <span className="text-white font-semibold text-sm">Addis Gaming Arena</span>
                  </div>
                  <p className="text-gray-400 text-xs">Bole Road, Addis Ababa, Ethiopia</p>
                </div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <MapPin className="h-4 w-4 text-red-400" />
                    <span className="text-white font-semibold text-sm">Merkato Gaming Hub</span>
                  </div>
                  <p className="text-gray-400 text-xs">Merkato District, Addis Ababa, Ethiopia</p>
                </div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <MapPin className="h-4 w-4 text-red-400" />
                    <span className="text-white font-semibold text-sm">Piazza Gaming Center</span>
                  </div>
                  <p className="text-gray-400 text-xs">Piazza Area, Addis Ababa, Ethiopia</p>
                </div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <MapPin className="h-4 w-4 text-red-400" />
                    <span className="text-white font-semibold text-sm">Kazanchis Pro Lounge</span>
                  </div>
                  <p className="text-gray-400 text-xs">Kazanchis, Addis Ababa, Ethiopia</p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
