'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Trophy, 
  Users, 
  Play, 
  Pause, 
  Settings, 
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  MapPin,
  Clock,
  Target
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Tabs, 
  <PERSON>bs<PERSON>ist, 
  TabsTrigger, 
  TabsContent,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  Badge,
  Modal,
  Input
} from '@/components/ui';

// Mock tournament data
const tournaments = [
  {
    id: 1,
    name: 'FIFA 24 Championship Finals',
    game: 'FIFA 24',
    status: 'Live',
    participants: 32,
    currentRound: 'Semi-Finals',
    startDate: '2024-01-15',
    prizePool: '$5,000',
    venue: 'Addis Gaming Arena',
  },
  {
    id: 2,
    name: 'FIFA Winter Cup',
    game: 'FIFA 24',
    status: 'Upcoming',
    participants: 16,
    currentRound: 'Registration',
    startDate: '2024-02-01',
    prizePool: '$2,500',
    venue: 'Merkato Gaming Hub',
  },
  {
    id: 3,
    name: 'FIFA Rookie League',
    game: 'FIFA 24',
    status: 'Registration Open',
    participants: 24,
    currentRound: 'Registration',
    startDate: '2024-02-15',
    prizePool: '$1,500',
    venue: 'Piazza Gaming Center',
  },
];

const matches = [
  {
    id: 1,
    tournament: 'FIFA 24 Championship Finals',
    team1: 'FifaKing_23',
    team2: 'GoalMachine',
    score1: 2,
    score2: 1,
    status: 'Live',
    round: 'Semi-Final',
    scheduledTime: '14:30',
    venue: 'Addis Gaming Arena',
  },
  {
    id: 2,
    tournament: 'FIFA 24 Championship Finals',
    team1: 'EthiopianLion',
    team2: 'AddisStriker',
    score1: 0,
    score2: 0,
    status: 'Upcoming',
    round: 'Semi-Final',
    scheduledTime: '16:00',
    venue: 'Addis Gaming Arena',
  },
];

export default function TournamentAdminDashboard() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedTournament, setSelectedTournament] = useState<any>(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">Tournament Admin</h1>
              <p className="text-gray-300">Manage tournaments, brackets, and live matches</p>
            </div>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Tournament
            </Button>
          </div>
        </motion.div>

        <Tabs defaultValue="tournaments" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tournaments">
              <Trophy className="h-4 w-4 mr-2" />
              Tournaments
            </TabsTrigger>
            <TabsTrigger value="matches">
              <Play className="h-4 w-4 mr-2" />
              Live Matches
            </TabsTrigger>
            <TabsTrigger value="brackets">
              <Target className="h-4 w-4 mr-2" />
              Brackets
            </TabsTrigger>
            <TabsTrigger value="display">
              <Eye className="h-4 w-4 mr-2" />
              Display Control
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tournaments">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">Tournament Management</h3>
                <div className="flex space-x-2">
                  <Button variant="secondary">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </div>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tournament</TableHead>
                    <TableHead>Game</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Participants</TableHead>
                    <TableHead>Current Round</TableHead>
                    <TableHead>Prize Pool</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tournaments.map((tournament) => (
                    <TableRow key={tournament.id}>
                      <TableCell className="font-medium">{tournament.name}</TableCell>
                      <TableCell>{tournament.game}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            tournament.status === 'Live' ? 'danger' : 
                            tournament.status === 'Upcoming' ? 'info' : 'success'
                          }
                        >
                          {tournament.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{tournament.participants}</TableCell>
                      <TableCell>{tournament.currentRound}</TableCell>
                      <TableCell>{tournament.prizePool}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="ghost">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </TabsContent>

          <TabsContent value="matches">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Live Match Control</h3>
                <div className="space-y-4">
                  {matches.filter(m => m.status === 'Live').map((match) => (
                    <div key={match.id} className="p-4 bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <Badge variant="danger">LIVE</Badge>
                        <span className="text-gray-300 text-sm">{match.round}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-4 items-center mb-4">
                        <div className="text-center">
                          <div className="text-white font-semibold">{match.team1}</div>
                          <div className="text-2xl font-bold text-blue-400">{match.score1}</div>
                        </div>
                        <div className="text-center text-gray-400">VS</div>
                        <div className="text-center">
                          <div className="text-white font-semibold">{match.team2}</div>
                          <div className="text-2xl font-bold text-red-400">{match.score2}</div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" className="flex-1">
                          <Plus className="h-4 w-4 mr-1" />
                          Team 1 +1
                        </Button>
                        <Button size="sm" variant="secondary" className="flex-1">
                          <Plus className="h-4 w-4 mr-1" />
                          Team 2 +1
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Pause className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Upcoming Matches</h3>
                <div className="space-y-4">
                  {matches.filter(m => m.status === 'Upcoming').map((match) => (
                    <div key={match.id} className="p-4 bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <Badge variant="info">Upcoming</Badge>
                        <div className="flex items-center text-gray-300 text-sm">
                          <Clock className="h-4 w-4 mr-1" />
                          {match.scheduledTime}
                        </div>
                      </div>
                      <div className="text-center mb-4">
                        <div className="text-white font-semibold">
                          {match.team1} vs {match.team2}
                        </div>
                        <div className="text-gray-400 text-sm">{match.round}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" className="flex-1">
                          <Play className="h-4 w-4 mr-1" />
                          Start Match
                        </Button>
                        <Button size="sm" variant="secondary">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="brackets">
            <Card className="p-6">
              <h3 className="text-xl font-bold text-white mb-6">Tournament Brackets</h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Quarter Finals */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4 text-center">Quarter Finals</h4>
                  <div className="space-y-4">
                    {[
                      { team1: 'Team Alpha', team2: 'Team Echo', winner: 'Team Alpha' },
                      { team1: 'Team Beta', team2: 'Team Foxtrot', winner: 'Team Beta' },
                      { team1: 'Team Gamma', team2: 'Team Golf', winner: 'Team Gamma' },
                      { team1: 'Team Delta', team2: 'Team Hotel', winner: 'Team Delta' },
                    ].map((match, index) => (
                      <div key={index} className="bg-gray-700 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <span className={`text-sm ${match.winner === match.team1 ? 'text-green-400 font-semibold' : 'text-gray-300'}`}>
                            {match.team1}
                          </span>
                          <span className="text-gray-500">vs</span>
                          <span className={`text-sm ${match.winner === match.team2 ? 'text-green-400 font-semibold' : 'text-gray-300'}`}>
                            {match.team2}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Semi Finals */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4 text-center">Semi Finals</h4>
                  <div className="space-y-4">
                    <div className="bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-green-400 font-semibold">Team Alpha</span>
                        <span className="text-gray-500">vs</span>
                        <span className="text-sm text-gray-300">Team Beta</span>
                      </div>
                      <div className="text-center mt-2">
                        <Badge variant="danger">LIVE</Badge>
                      </div>
                    </div>
                    <div className="bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-300">Team Gamma</span>
                        <span className="text-gray-500">vs</span>
                        <span className="text-sm text-gray-300">Team Delta</span>
                      </div>
                      <div className="text-center mt-2">
                        <Badge variant="info">16:00</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Finals */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4 text-center">Finals</h4>
                  <div className="space-y-4">
                    <div className="bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">TBD</span>
                        <span className="text-gray-500">vs</span>
                        <span className="text-sm text-gray-500">TBD</span>
                      </div>
                      <div className="text-center mt-2">
                        <Badge variant="warning">Pending</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="display">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Display Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Show Live Scores</span>
                    <Button size="sm" variant="success">ON</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Display Brackets</span>
                    <Button size="sm" variant="success">ON</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Show Player Names</span>
                    <Button size="sm" variant="secondary">OFF</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Viewer Count</span>
                    <Button size="sm" variant="success">ON</Button>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Live Display Control</h3>
                <div className="space-y-4">
                  <Button
                    className="w-full"
                    onClick={() => window.open('/display', '_blank')}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Open Display View
                  </Button>
                  <Button variant="secondary" className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Display Settings
                  </Button>
                  <Button variant="secondary" className="w-full">
                    <Play className="h-4 w-4 mr-2" />
                    Start Stream Overlay
                  </Button>
                  <Button variant="ghost" className="w-full">
                    <Pause className="h-4 w-4 mr-2" />
                    Hide Display
                  </Button>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Tournament Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Tournament"
        size="lg"
      >
        <div className="space-y-4">
          <Input label="Tournament Name" placeholder="Enter tournament name" />
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-300">Game</label>
              <select
                className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white"
                aria-label="Select game"
              >
                <option>FIFA 24</option>
                <option>FIFA 23 (Legacy)</option>
              </select>
            </div>
            <Input label="Prize Pool" placeholder="$0" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Input label="Max Participants" type="number" placeholder="32" />
            <Input label="Start Date" type="date" />
          </div>
          <div className="flex space-x-4 pt-4">
            <Button className="flex-1">Create Tournament</Button>
            <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </Modal>

      <Footer />
    </div>
  );
}
