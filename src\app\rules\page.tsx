'use client';

import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Gamepad2, 
  Clock, 
  Trophy, 
  AlertCircle, 
  CheckCircle,
  Users,
  Settings,
  Shield,
  Target
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Card, Badge } from '@/components/ui';

const rulesSections = [
  {
    title: 'General Tournament Rules',
    icon: BookOpen,
    rules: [
      'All tournaments are played on FIFA 24 using PlayStation 5 consoles',
      'Matches are played on Professional difficulty level',
      'Match duration: 6 minutes per half (12 minutes total)',
      'Weather conditions: Clear, no wind',
      'All matches are single elimination unless specified otherwise',
      'Tournament officials have final authority on all decisions'
    ]
  },
  {
    title: 'Player Requirements',
    icon: Users,
    rules: [
      'Players must be 16 years or older (parental consent required for under 18)',
      'Valid identification required for registration and prize collection',
      'Players must arrive 30 minutes before scheduled match time',
      'Professional conduct expected at all times',
      'Players may bring their own controllers (must be official PS5 controllers)',
      'No coaching or assistance allowed during matches'
    ]
  },
  {
    title: 'Match Format',
    icon: Target,
    rules: [
      'Single elimination bracket format',
      'Best of 1 for early rounds, Best of 3 for semi-finals and finals',
      'Extra time and penalties if tied after regulation',
      'Team selection: Ultimate Team or Seasons mode (specified per tournament)',
      'No pausing allowed except for technical issues',
      'Maximum 2 substitutions per match'
    ]
  },
  {
    title: 'Equipment and Setup',
    icon: Settings,
    rules: [
      'All matches played on venue-provided PS5 consoles and monitors',
      'Official FIFA 24 game with latest updates',
      'Controllers provided by venue (players may bring their own)',
      'Headphones/audio equipment provided or players may bring their own',
      'No external devices or modifications allowed',
      'Technical issues must be reported immediately to officials'
    ]
  },
  {
    title: 'Prohibited Actions',
    icon: Shield,
    rules: [
      'Glitching, exploiting, or using any game bugs',
      'Unsportsmanlike conduct or harassment',
      'Coaching or receiving assistance during matches',
      'Intentional disconnection or game manipulation',
      'Use of performance-enhancing software or hardware',
      'Sharing accounts or playing under false identity'
    ]
  },
  {
    title: 'Penalties and Enforcement',
    icon: AlertCircle,
    rules: [
      'First offense: Warning and potential match restart',
      'Second offense: Automatic loss of current match',
      'Severe violations: Immediate disqualification from tournament',
      'Repeated violations: Ban from future tournaments',
      'All penalties are at the discretion of tournament officials',
      'Appeals must be made within 24 hours of the incident'
    ]
  }
];

const tournamentFormats = [
  {
    name: 'Championship Finals',
    format: 'Single Elimination',
    participants: '32 players',
    rounds: '5 rounds',
    duration: '6-8 hours',
    prizePool: '$5,000'
  },
  {
    name: 'Winter Cup',
    format: 'Single Elimination',
    participants: '16 players',
    rounds: '4 rounds',
    duration: '4-5 hours',
    prizePool: '$2,500'
  },
  {
    name: 'Rookie League',
    format: 'Round Robin + Playoffs',
    participants: '24 players',
    rounds: 'Groups + 3 rounds',
    duration: '5-6 hours',
    prizePool: '$1,500'
  }
];

export default function RulesPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <BookOpen className="h-16 w-16 text-blue-400 mx-auto mb-4" />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Tournament Rules
          </h1>
          <p className="text-xl text-gray-300">
            Official rules and regulations for FIFA 24 tournaments in Ethiopia
          </p>
        </motion.div>

        {/* Quick Reference */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-12"
        >
          <Card className="p-6 bg-gradient-to-r from-blue-900/20 to-blue-800/20 border-blue-600/30">
            <h2 className="text-2xl font-bold text-white mb-4 text-center">Quick Reference</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div>
                <Gamepad2 className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <p className="text-white font-semibold">FIFA 24</p>
                <p className="text-gray-300 text-sm">PlayStation 5</p>
              </div>
              <div>
                <Clock className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-white font-semibold">12 Minutes</p>
                <p className="text-gray-300 text-sm">6 min halves</p>
              </div>
              <div>
                <Users className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <p className="text-white font-semibold">16+ Years</p>
                <p className="text-gray-300 text-sm">Age requirement</p>
              </div>
              <div>
                <Trophy className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-white font-semibold">Single Elim</p>
                <p className="text-gray-300 text-sm">Tournament format</p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Rules Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {rulesSections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * (index + 2) }}
            >
              <Card className="p-6 h-full">
                <div className="flex items-center mb-4">
                  <section.icon className="h-6 w-6 text-blue-400 mr-3" />
                  <h2 className="text-xl font-bold text-white">{section.title}</h2>
                </div>
                <ul className="space-y-3">
                  {section.rules.map((rule, ruleIndex) => (
                    <li key={ruleIndex} className="text-gray-300 flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{rule}</span>
                    </li>
                  ))}
                </ul>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Tournament Formats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-12"
        >
          <h2 className="text-3xl font-bold text-white text-center mb-8">Tournament Formats</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {tournamentFormats.map((tournament, index) => (
              <Card key={index} className="p-6 text-center">
                <h3 className="text-xl font-bold text-white mb-4">{tournament.name}</h3>
                <div className="space-y-3">
                  <div>
                    <Badge variant="info" className="mb-2">{tournament.format}</Badge>
                    <p className="text-gray-300 text-sm">{tournament.participants}</p>
                  </div>
                  <div className="border-t border-gray-700 pt-3">
                    <p className="text-gray-300 text-sm"><strong>Rounds:</strong> {tournament.rounds}</p>
                    <p className="text-gray-300 text-sm"><strong>Duration:</strong> {tournament.duration}</p>
                    <p className="text-green-400 font-semibold"><strong>Prize:</strong> {tournament.prizePool}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Important Notes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="space-y-6"
        >
          <Card className="p-6 bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-600/30">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-6 w-6 text-yellow-400 mr-3" />
              <h2 className="text-xl font-bold text-white">Important Notes</h2>
            </div>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-yellow-400 mr-2 mt-1">•</span>
                <span>Tournament rules may be updated. Check for announcements before each event.</span>
              </li>
              <li className="flex items-start">
                <span className="text-yellow-400 mr-2 mt-1">•</span>
                <span>All participants must agree to these rules during registration.</span>
              </li>
              <li className="flex items-start">
                <span className="text-yellow-400 mr-2 mt-1">•</span>
                <span>Tournament officials reserve the right to modify rules for specific events.</span>
              </li>
              <li className="flex items-start">
                <span className="text-yellow-400 mr-2 mt-1">•</span>
                <span>Disputes should be raised immediately with tournament officials.</span>
              </li>
            </ul>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Fair Play Commitment</h2>
            <p className="text-gray-300 mb-4">
              Ethiopia Gaming Events is committed to providing a fair, competitive, and enjoyable 
              tournament experience for all participants. We expect all players to:
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5" />
                <span>Compete with integrity and sportsmanship</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5" />
                <span>Respect opponents, officials, and venue staff</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5" />
                <span>Follow all rules and accept official decisions</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5" />
                <span>Contribute to a positive gaming community</span>
              </li>
            </ul>
          </Card>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
