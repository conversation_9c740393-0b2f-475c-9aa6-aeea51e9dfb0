'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Setting<PERSON>,
  Users,
  Trophy,
  Monitor,
  Database,
  Shield,
  BarChart3,
  FileText,
  Image,
  Palette,
  Globe,
  Activity
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { 
  <PERSON><PERSON>, 
  Card, 
  Tabs, 
  TabsList, 
  TabsTrigger, 
  TabsContent,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  Badge
} from '@/components/ui';

// Mock data
const systemStats = {
  totalUsers: 1247,
  activeTournaments: 5,
  totalMatches: 342,
  systemUptime: '99.9%',
};

const recentUsers = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Player', status: 'Active', joinDate: '2024-01-15' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Tournament Admin', status: 'Active', joinDate: '2024-01-14' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Player', status: 'Inactive', joinDate: '2024-01-13' },
];

const systemLogs = [
  { id: 1, timestamp: '2024-01-15 14:30:25', level: 'INFO', message: 'User registration completed', user: '<EMAIL>' },
  { id: 2, timestamp: '2024-01-15 14:25:12', level: 'WARNING', message: 'High server load detected', user: 'system' },
  { id: 3, timestamp: '2024-01-15 14:20:08', level: 'ERROR', message: 'Database connection timeout', user: 'system' },
];

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-2">IT Admin Dashboard</h1>
          <p className="text-gray-300">Manage your gaming tournament platform</p>
        </motion.div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">
              <BarChart3 className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="users">
              <Users className="h-4 w-4 mr-2" />
              Users
            </TabsTrigger>
            <TabsTrigger value="content">
              <FileText className="h-4 w-4 mr-2" />
              Content
            </TabsTrigger>
            <TabsTrigger value="themes">
              <Palette className="h-4 w-4 mr-2" />
              Themes
            </TabsTrigger>
            <TabsTrigger value="system">
              <Database className="h-4 w-4 mr-2" />
              System
            </TabsTrigger>
            <TabsTrigger value="security">
              <Shield className="h-4 w-4 mr-2" />
              Security
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Total Users</p>
                      <p className="text-3xl font-bold text-white">{systemStats.totalUsers.toLocaleString()}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-400" />
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Active Tournaments</p>
                      <p className="text-3xl font-bold text-white">{systemStats.activeTournaments}</p>
                    </div>
                    <Trophy className="h-8 w-8 text-yellow-400" />
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Total Matches</p>
                      <p className="text-3xl font-bold text-white">{systemStats.totalMatches}</p>
                    </div>
                    <Monitor className="h-8 w-8 text-green-400" />
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">System Uptime</p>
                      <p className="text-3xl font-bold text-white">{systemStats.systemUptime}</p>
                    </div>
                    <Activity className="h-8 w-8 text-red-400" />
                  </div>
                </Card>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {systemLogs.map((log) => (
                    <div key={log.id} className="flex items-start space-x-3">
                      <Badge 
                        variant={log.level === 'ERROR' ? 'danger' : log.level === 'WARNING' ? 'warning' : 'info'}
                        size="sm"
                      >
                        {log.level}
                      </Badge>
                      <div className="flex-1">
                        <p className="text-gray-300 text-sm">{log.message}</p>
                        <p className="text-gray-500 text-xs">{log.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-4">
                  <Button className="h-20 flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    Manage Users
                  </Button>
                  <Button variant="secondary" className="h-20 flex-col">
                    <Trophy className="h-6 w-6 mb-2" />
                    Create Tournament
                  </Button>
                  <Button
                    variant="secondary"
                    className="h-20 flex-col"
                    onClick={() => window.open('/display', '_blank')}
                  >
                    <Monitor className="h-6 w-6 mb-2" />
                    Display View
                  </Button>
                  <Button variant="secondary" className="h-20 flex-col">
                    <Settings className="h-6 w-6 mb-2" />
                    System Settings
                  </Button>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">User Management</h3>
                <Button>
                  <Users className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Join Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell>
                        <Badge variant={user.status === 'Active' ? 'success' : 'warning'}>
                          {user.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.joinDate}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="ghost">Edit</Button>
                          <Button size="sm" variant="danger">Delete</Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </TabsContent>

          <TabsContent value="content">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Website Content</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-400" />
                      <span className="text-white">Homepage Content</span>
                    </div>
                    <Button size="sm" variant="ghost">Edit</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-400" />
                      <span className="text-white">Tournament Rules</span>
                    </div>
                    <Button size="sm" variant="ghost">Edit</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-400" />
                      <span className="text-white">Registration Forms</span>
                    </div>
                    <Button size="sm" variant="ghost">Edit</Button>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Media Management</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Image className="h-5 w-5 text-green-400" />
                      <span className="text-white">Tournament Banners</span>
                    </div>
                    <Button size="sm" variant="ghost">Manage</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Image className="h-5 w-5 text-green-400" />
                      <span className="text-white">Team Logos</span>
                    </div>
                    <Button size="sm" variant="ghost">Manage</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Image className="h-5 w-5 text-green-400" />
                      <span className="text-white">Background Images</span>
                    </div>
                    <Button size="sm" variant="ghost">Manage</Button>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="themes">
            <Card className="p-6">
              <h3 className="text-xl font-bold text-white mb-6">Theme Customization</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white">Color Scheme</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
                      <span className="text-gray-300">Primary Color</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-700 rounded-full"></div>
                      <span className="text-gray-300">Secondary Color</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-red-500 rounded-full"></div>
                      <span className="text-gray-300">Accent Color</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white">Typography</h4>
                  <div className="space-y-2">
                    <div className="p-3 bg-gray-700 rounded">
                      <span className="text-white font-bold">Heading Font</span>
                    </div>
                    <div className="p-3 bg-gray-700 rounded">
                      <span className="text-white">Body Font</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white">Layout</h4>
                  <div className="space-y-2">
                    <Button variant="secondary" className="w-full justify-start">
                      <Globe className="h-4 w-4 mr-2" />
                      Header Settings
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      <Monitor className="h-4 w-4 mr-2" />
                      Footer Settings
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex space-x-4">
                <Button>Save Changes</Button>
                <Button variant="secondary">Preview</Button>
                <Button variant="ghost">Reset to Default</Button>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="system">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">System Information</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Server Status</span>
                    <Badge variant="success">Online</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Database Status</span>
                    <Badge variant="success">Connected</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Backup</span>
                    <span className="text-white">2024-01-15 02:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Storage Used</span>
                    <span className="text-white">45.2 GB / 100 GB</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">System Actions</h3>
                <div className="space-y-3">
                  <Button variant="secondary" className="w-full justify-start">
                    <Database className="h-4 w-4 mr-2" />
                    Create Backup
                  </Button>
                  <Button variant="secondary" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    System Settings
                  </Button>
                  <Button variant="secondary" className="w-full justify-start">
                    <Activity className="h-4 w-4 mr-2" />
                    View Logs
                  </Button>
                  <Button variant="danger" className="w-full justify-start">
                    <Shield className="h-4 w-4 mr-2" />
                    Restart System
                  </Button>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="security">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Security Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Two-Factor Authentication</span>
                    <Badge variant="success">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">SSL Certificate</span>
                    <Badge variant="success">Valid</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Firewall Status</span>
                    <Badge variant="success">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Last Security Scan</span>
                    <span className="text-white">2024-01-15 08:00</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-bold text-white mb-4">Access Control</h3>
                <div className="space-y-3">
                  <Button variant="secondary" className="w-full justify-start">
                    <Shield className="h-4 w-4 mr-2" />
                    Manage Permissions
                  </Button>
                  <Button variant="secondary" className="w-full justify-start">
                    <Users className="h-4 w-4 mr-2" />
                    Admin Users
                  </Button>
                  <Button variant="secondary" className="w-full justify-start">
                    <Activity className="h-4 w-4 mr-2" />
                    Security Logs
                  </Button>
                  <Button variant="secondary" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    Security Policies
                  </Button>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
}
