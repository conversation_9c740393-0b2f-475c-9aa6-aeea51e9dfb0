'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Trophy,
  Users,
  Calendar,
  MapPin,
  Gamepad2,
  Sparkles,
  Award,
  Shield,
  TrendingUp
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { <PERSON><PERSON>, Card, Badge } from '@/components/ui';

const stats = [
  { label: 'Active Players', value: '2,500+', icon: Users },
  { label: 'Tournaments Held', value: '150+', icon: Trophy },
  { label: 'Prize Money Awarded', value: '$75,000+', icon: Award },
  { label: 'Gaming Venues', value: '4', icon: MapPin },
];

const features = [
  {
    icon: Trophy,
    title: 'Professional Tournaments',
    description: 'Compete in officially organized FIFA 24 tournaments with standardized rules and professional oversight.',
  },
  {
    icon: MapPin,
    title: 'Premium Venues',
    description: 'Play at state-of-the-art gaming facilities across Addis Ababa with top-tier equipment and comfortable environments.',
  },
  {
    icon: Users,
    title: 'Skilled Competition',
    description: 'Face off against Ethiopia&apos;s best FIFA players in a competitive and fair gaming environment.',
  },
  {
    icon: Award,
    title: 'Real Prizes',
    description: 'Win substantial cash prizes, trophies, and recognition in Ethiopia\'s growing esports community.',
  },
  {
    icon: Shield,
    title: 'Fair Play',
    description: 'All tournaments follow strict anti-cheat policies and fair play guidelines for competitive integrity.',
  },
  {
    icon: TrendingUp,
    title: 'Skill Development',
    description: 'Improve your FIFA skills through competitive play and learn from the best players in Ethiopia.',
  },
];

const upcomingTournaments = [
  {
    title: 'FIFA 24 Championship Finals',
    date: '2024-02-15',
    prize: '$5,000',
    participants: '32 players',
    venue: 'Addis Gaming Arena',
    status: 'Registration Open',
  },
  {
    title: 'FIFA Winter Cup',
    date: '2024-02-28',
    prize: '$2,500',
    participants: '16 players',
    venue: 'Merkato Gaming Hub',
    status: 'Coming Soon',
  },
  {
    title: 'FIFA Rookie League',
    date: '2024-03-10',
    prize: '$1,500',
    participants: '24 players',
    venue: 'Piazza Gaming Center',
    status: 'Coming Soon',
  },
];

export default function Home() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen bg-gray-950 relative overflow-hidden">
      {/* Interactive cursor background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-950 via-gray-900 to-black">
        <div
          className="absolute w-96 h-96 bg-blue-500/10 rounded-full blur-3xl transition-all duration-300 ease-out pointer-events-none"
          style={{
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
        />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_70%)] pointer-events-none" />
      </div>

      <div className="relative z-10">
        <Navigation />

        {/* Hero Section */}
        <section className="relative overflow-hidden py-20 lg:py-32">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <Gamepad2 className="h-20 w-20 text-green-400" />
                  <motion.div
                    className="absolute -top-2 -right-2"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Sparkles className="h-8 w-8 text-yellow-400" />
                  </motion.div>
                </div>
              </div>

              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
                Ethiopia&apos;s Premier
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-green-400">
                  FIFA Gaming Hub
                </span>
              </h1>

              <p className="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
                Experience the thrill of competitive gaming in person in Addis Ababa, Ethiopia! Currently featuring FIFA 24 on PS5,
                with exciting plans to expand to CS:GO, Valorant, Dota 2, and more.
              </p>

              <div className="flex flex-wrap justify-center gap-3 mb-8">
                <Badge variant="success" className="text-sm px-4 py-2">
                  <Gamepad2 className="h-4 w-4 mr-2" />
                  PS5 Exclusive
                </Badge>
                <Badge variant="info" className="text-sm px-4 py-2">
                  <MapPin className="h-4 w-4 mr-2" />
                  In-Person Only
                </Badge>
                <Badge variant="warning" className="text-sm px-4 py-2">
                  <Trophy className="h-4 w-4 mr-2" />
                  Cash Prizes
                </Badge>
                <Badge variant="default" className="text-sm px-4 py-2">
                  <Users className="h-4 w-4 mr-2" />
                  All Skill Levels
                </Badge>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/tournaments">
                  <Button size="lg" className="text-lg px-8 py-4">
                    <Trophy className="mr-2 h-5 w-5" />
                    View Tournaments
                  </Button>
                </a>
                <a href="/register">
                  <Button variant="secondary" size="lg" className="text-lg px-8 py-4">
                    <Users className="mr-2 h-5 w-5" />
                    Register Now
                  </Button>
                </a>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-gray-900/50 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <stat.icon className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Why Choose Ethiopia Gaming Events?
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                We provide the ultimate competitive FIFA gaming experience with professional organization,
                premium venues, and substantial prizes.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="group"
                >
                  <Card className="p-6 h-full bg-gray-800/50 border-gray-700 hover:border-blue-500/50 transition-all duration-300">
                    <feature.icon className="h-12 w-12 text-blue-400 mb-4 group-hover:text-blue-300 transition-colors" />
                    <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Upcoming Tournaments */}
        <section className="py-20 bg-gray-900/30 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Upcoming Tournaments
              </h2>
              <p className="text-xl text-gray-300">
                Join the next generation of FIFA champions in Ethiopia
              </p>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {upcomingTournaments.map((tournament, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02 }}
                >
                  <Card className="p-6 bg-gray-800/50 border-gray-700 hover:border-green-500/50 transition-all duration-300">
                    <div className="flex justify-between items-start mb-4">
                      <Trophy className="h-8 w-8 text-yellow-400" />
                      <Badge
                        variant={tournament.status === 'Registration Open' ? 'success' : 'default'}
                        className="text-xs"
                      >
                        {tournament.status}
                      </Badge>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-2">{tournament.title}</h3>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-gray-300 text-sm">
                        <Calendar className="h-4 w-4 mr-2 text-blue-400" />
                        <span>{new Date(tournament.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center text-gray-300 text-sm">
                        <Award className="h-4 w-4 mr-2 text-green-400" />
                        <span>{tournament.prize} Prize Pool</span>
                      </div>
                      <div className="flex items-center text-gray-300 text-sm">
                        <Users className="h-4 w-4 mr-2 text-purple-400" />
                        <span>{tournament.participants}</span>
                      </div>
                      <div className="flex items-center text-gray-300 text-sm">
                        <MapPin className="h-4 w-4 mr-2 text-red-400" />
                        <span>{tournament.venue}</span>
                      </div>
                    </div>

                    {tournament.status === 'Registration Open' && (
                      <a href="/register">
                        <Button className="w-full" size="sm">
                          Register Now
                        </Button>
                      </a>
                    )}
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Venues Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Premium Gaming Venues in Addis Ababa
              </h2>
              <p className="text-gray-300 text-lg mb-6">
                Our tournaments are hosted at state-of-the-art gaming facilities across Addis Ababa, Ethiopia,
                equipped with the latest technology and comfortable gaming environments.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
            >
              {[
                { name: 'Addis Gaming Arena', address: 'Bole Road, Addis Ababa', capacity: '50 players' },
                { name: 'Merkato Gaming Hub', address: 'Merkato District, Addis Ababa', capacity: '30 players' },
                { name: 'Piazza Gaming Center', address: 'Piazza Area, Addis Ababa', capacity: '40 players' },
                { name: 'Kazanchis Pro Lounge', address: 'Kazanchis, Addis Ababa', capacity: '60 players' },
              ].map((venue, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="p-4 bg-gray-700/50 border-gray-600">
                    <MapPin className="h-6 w-6 text-red-400 mb-2" />
                    <h4 className="text-white font-semibold text-sm mb-1">{venue.name}</h4>
                    <p className="text-gray-400 text-xs mb-1">{venue.address}</p>
                    <p className="text-gray-500 text-xs">{venue.capacity}</p>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-blue-900/20 to-green-900/20 rounded-2xl p-12 border border-blue-500/20"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Trophy className="h-16 w-16 text-yellow-400 mx-auto mb-6" />
              </motion.div>

              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Compete?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join our FIFA 24 tournaments and prove you&apos;re the best player in Ethiopia!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/tournaments">
                  <Button size="lg" className="text-lg px-8 py-4">
                    <Trophy className="mr-2 h-5 w-5" />
                    View Tournaments
                  </Button>
                </a>
                <a href="/register">
                  <Button variant="secondary" size="lg" className="text-lg px-8 py-4">
                    <Users className="mr-2 h-5 w-5" />
                    Register Now
                  </Button>
                </a>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
}