'use client';

import { motion } from 'framer-motion';
import { Trophy, Medal, Crown, Star, TrendingUp, Calendar, Gamepad2 } from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Card, Badge, Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui';

const topPlayers = [
  {
    rank: 1,
    name: 'FifaKing_23',
    realName: '<PERSON>',
    points: 2850,
    wins: 45,
    losses: 8,
    winRate: 84.9,
    tournaments: 12,
    earnings: 15750,
    recentForm: ['W', 'W', 'W', 'W', 'W']
  },
  {
    rank: 2,
    name: 'GoalMachine',
    realName: '<PERSON>',
    points: 2720,
    wins: 42,
    losses: 11,
    winRate: 79.2,
    tournaments: 11,
    earnings: 12300,
    recentForm: ['W', 'W', 'L', 'W', 'W']
  },
  {
    rank: 3,
    name: '<PERSON><PERSON><PERSON>',
    realName: '<PERSON>',
    points: 2650,
    wins: 38,
    losses: 9,
    winRate: 80.9,
    tournaments: 10,
    earnings: 9800,
    recentForm: ['W', 'L', 'W', 'W', 'W']
  }
];

const allPlayers = [
  ...topPlayers,
  {
    rank: 4,
    name: 'Addis<PERSON>triker',
    realName: '<PERSON>e',
    points: 2580,
    wins: 35,
    losses: 12,
    winRate: 74.5,
    tournaments: 9,
    earnings: 7200,
    recentForm: ['W', 'W', 'W', 'L', 'W']
  },
  {
    rank: 5,
    name: 'SkillMaster',
    real<PERSON>ame: 'David Haile',
    points: 2510,
    wins: 33,
    losses: 10,
    winRate: 76.7,
    tournaments: 8,
    earnings: 6500,
    recentForm: ['L', 'W', 'W', 'W', 'L']
  },
  {
    rank: 6,
    name: 'ProGamer_ET',
    realName: 'Yonas Girma',
    points: 2450,
    wins: 31,
    losses: 14,
    winRate: 68.9,
    tournaments: 9,
    earnings: 5800,
    recentForm: ['W', 'L', 'W', 'L', 'W']
  },
  {
    rank: 7,
    name: 'FifaLegend',
    realName: 'Abraham Tesfaye',
    points: 2380,
    wins: 29,
    losses: 11,
    winRate: 72.5,
    tournaments: 7,
    earnings: 4900,
    recentForm: ['W', 'W', 'L', 'W', 'W']
  },
  {
    rank: 8,
    name: 'GameChanger',
    realName: 'Robel Assefa',
    points: 2320,
    wins: 27,
    losses: 13,
    winRate: 67.5,
    tournaments: 8,
    earnings: 4200,
    recentForm: ['L', 'W', 'W', 'W', 'L']
  }
];

const recentTournaments = [
  {
    name: 'FIFA 24 Championship Finals',
    date: '2024-01-15',
    winner: 'FifaKing_23',
    prize: '$5,000',
    participants: 32
  },
  {
    name: 'FIFA New Year Classic',
    date: '2024-01-01',
    winner: 'GoalMachine',
    prize: '$4,000',
    participants: 32
  },
  {
    name: 'FIFA Winter Warm-up',
    date: '2023-12-20',
    winner: 'EthiopianLion',
    prize: '$2,500',
    participants: 16
  }
];

export default function LeaderboardPage() {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-400" />;
      case 2:
        return <Medal className="h-6 w-6 text-gray-400" />;
      case 3:
        return <Medal className="h-6 w-6 text-amber-600" />;
      default:
        return <span className="text-lg font-bold text-gray-400">#{rank}</span>;
    }
  };

  const getFormColor = (result: string) => {
    return result === 'W' ? 'bg-green-600' : 'bg-red-600';
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Trophy className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Leaderboard
          </h1>
          <p className="text-xl text-gray-300">
            Top FIFA 24 players in Ethiopia's competitive scene
          </p>
        </motion.div>

        {/* Top 3 Players */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {topPlayers.map((player, index) => (
            <motion.div
              key={player.rank}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`${index === 0 ? 'md:order-2 md:scale-110' : index === 1 ? 'md:order-1' : 'md:order-3'}`}
            >
              <Card className={`p-6 text-center ${
                index === 0 ? 'bg-gradient-to-br from-yellow-900/20 to-yellow-800/20 border-yellow-600/30' :
                index === 1 ? 'bg-gradient-to-br from-gray-700/20 to-gray-600/20 border-gray-500/30' :
                'bg-gradient-to-br from-amber-900/20 to-amber-800/20 border-amber-600/30'
              }`}>
                <div className="flex justify-center mb-4">
                  {getRankIcon(player.rank)}
                </div>
                <h3 className="text-xl font-bold text-white mb-1">{player.name}</h3>
                <p className="text-gray-400 text-sm mb-4">{player.realName}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Points:</span>
                    <span className="text-white font-semibold">{player.points.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Win Rate:</span>
                    <span className="text-green-400 font-semibold">{player.winRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Earnings:</span>
                    <span className="text-yellow-400 font-semibold">${player.earnings.toLocaleString()}</span>
                  </div>
                </div>

                <div className="flex justify-center space-x-1">
                  {player.recentForm.map((result, i) => (
                    <div
                      key={i}
                      className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${getFormColor(result)}`}
                    >
                      {result}
                    </div>
                  ))}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Full Leaderboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-12"
        >
          <Card className="p-6">
            <h2 className="text-2xl font-bold text-white mb-6">Full Rankings</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rank</TableHead>
                  <TableHead>Player</TableHead>
                  <TableHead>Points</TableHead>
                  <TableHead>W/L</TableHead>
                  <TableHead>Win Rate</TableHead>
                  <TableHead>Tournaments</TableHead>
                  <TableHead>Earnings</TableHead>
                  <TableHead>Form</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allPlayers.map((player) => (
                  <TableRow key={player.rank}>
                    <TableCell>
                      <div className="flex items-center">
                        {player.rank <= 3 ? getRankIcon(player.rank) : `#${player.rank}`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-semibold text-white">{player.name}</div>
                        <div className="text-sm text-gray-400">{player.realName}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-semibold">{player.points.toLocaleString()}</TableCell>
                    <TableCell>{player.wins}-{player.losses}</TableCell>
                    <TableCell>
                      <Badge variant={player.winRate >= 80 ? 'success' : player.winRate >= 70 ? 'warning' : 'default'}>
                        {player.winRate}%
                      </Badge>
                    </TableCell>
                    <TableCell>{player.tournaments}</TableCell>
                    <TableCell className="text-yellow-400 font-semibold">
                      ${player.earnings.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        {player.recentForm.map((result, i) => (
                          <div
                            key={i}
                            className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white ${getFormColor(result)}`}
                          >
                            {result}
                          </div>
                        ))}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </motion.div>

        {/* Recent Tournament Winners */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="p-6">
            <h2 className="text-2xl font-bold text-white mb-6">Recent Tournament Winners</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {recentTournaments.map((tournament, index) => (
                <div key={index} className="p-4 bg-gray-700 rounded-lg">
                  <div className="flex items-center mb-3">
                    <Trophy className="h-5 w-5 text-yellow-400 mr-2" />
                    <h3 className="font-semibold text-white text-sm">{tournament.name}</h3>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Winner:</span>
                      <span className="text-white font-semibold">{tournament.winner}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Prize:</span>
                      <span className="text-yellow-400 font-semibold">{tournament.prize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Date:</span>
                      <span className="text-gray-300">{new Date(tournament.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Players:</span>
                      <span className="text-gray-300">{tournament.participants}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
