# Supabase Integration Plan

## Overview
This document outlines the complete integration plan for adding <PERSON><PERSON><PERSON> as the backend for the Gaming Tournament Platform. The integration will provide authentication, real-time database functionality, and API endpoints.

## Database Schema

### 1. Users Table
```sql
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100),
  avatar_url TEXT,
  role VARCHAR(20) DEFAULT 'player' CHECK (role IN ('player', 'tournament_admin', 'it_admin')),
  phone VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Tournaments Table
```sql
CREATE TABLE tournaments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  game VARCHAR(100) NOT NULL,
  max_participants INTEGER NOT NULL,
  current_participants INTEGER DEFAULT 0,
  prize_pool DECIMAL(10,2),
  status VARCHAR(20) DEFAULT 'registration_open' CHECK (status IN ('registration_open', 'upcoming', 'live', 'completed', 'cancelled')),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  location VARCHAR(255),
  rules TEXT,
  banner_image_url TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Tournament Registrations Table
```sql
CREATE TABLE tournament_registrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  team_name VARCHAR(100),
  team_size INTEGER DEFAULT 1,
  experience_level VARCHAR(20) CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'professional')),
  registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'registered' CHECK (status IN ('registered', 'confirmed', 'cancelled')),
  UNIQUE(tournament_id, user_id)
);
```

### 4. Matches Table
```sql
CREATE TABLE matches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE,
  round VARCHAR(50) NOT NULL,
  match_number INTEGER NOT NULL,
  team1_id UUID REFERENCES tournament_registrations(id),
  team2_id UUID REFERENCES tournament_registrations(id),
  team1_score INTEGER DEFAULT 0,
  team2_score INTEGER DEFAULT 0,
  winner_id UUID REFERENCES tournament_registrations(id),
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled')),
  scheduled_time TIMESTAMP WITH TIME ZONE,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Live Display Settings Table
```sql
CREATE TABLE live_display_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE,
  show_scores BOOLEAN DEFAULT true,
  show_brackets BOOLEAN DEFAULT true,
  show_player_names BOOLEAN DEFAULT false,
  show_viewer_count BOOLEAN DEFAULT true,
  theme_color VARCHAR(7) DEFAULT '#3B82F6',
  background_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. System Logs Table
```sql
CREATE TABLE system_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  level VARCHAR(10) NOT NULL CHECK (level IN ('INFO', 'WARNING', 'ERROR')),
  message TEXT NOT NULL,
  user_id UUID REFERENCES users(id),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Row Level Security (RLS) Policies

### Users Table Policies
```sql
-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- IT admins can view all users
CREATE POLICY "IT admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'it_admin'
    )
  );
```

### Tournaments Table Policies
```sql
-- Anyone can view tournaments
CREATE POLICY "Anyone can view tournaments" ON tournaments
  FOR SELECT USING (true);

-- Tournament admins and IT admins can create tournaments
CREATE POLICY "Admins can create tournaments" ON tournaments
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('tournament_admin', 'it_admin')
    )
  );

-- Tournament creators and IT admins can update tournaments
CREATE POLICY "Tournament creators can update" ON tournaments
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'it_admin'
    )
  );
```

### Tournament Registrations Policies
```sql
-- Users can view registrations for tournaments they're in
CREATE POLICY "Users can view own registrations" ON tournament_registrations
  FOR SELECT USING (user_id = auth.uid());

-- Users can register for tournaments
CREATE POLICY "Users can register for tournaments" ON tournament_registrations
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Tournament admins can view all registrations for their tournaments
CREATE POLICY "Tournament admins can view registrations" ON tournament_registrations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tournaments t
      JOIN users u ON u.id = auth.uid()
      WHERE t.id = tournament_id 
      AND (t.created_by = auth.uid() OR u.role = 'it_admin')
    )
  );
```

## Authentication Setup

### 1. Supabase Auth Configuration
- Enable email/password authentication
- Configure email templates for signup/reset
- Set up OAuth providers (Google, Discord, etc.)
- Configure JWT settings

### 2. User Roles and Permissions
- **Player**: Can register for tournaments, view public data
- **Tournament Admin**: Can create/manage tournaments, view registrations
- **IT Admin**: Full system access, user management, system settings

## Real-time Subscriptions

### 1. Live Match Updates
```javascript
// Subscribe to match score updates
const matchSubscription = supabase
  .channel('match-updates')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'matches',
    filter: `tournament_id=eq.${tournamentId}`
  }, (payload) => {
    // Update live display with new scores
    updateMatchDisplay(payload.new);
  })
  .subscribe();
```

### 2. Tournament Registration Updates
```javascript
// Subscribe to new registrations
const registrationSubscription = supabase
  .channel('registration-updates')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'tournament_registrations'
  }, (payload) => {
    // Update participant count
    updateParticipantCount(payload.new);
  })
  .subscribe();
```

## API Endpoints Structure

### 1. Tournament Management
- `GET /api/tournaments` - List all tournaments
- `POST /api/tournaments` - Create new tournament
- `GET /api/tournaments/[id]` - Get tournament details
- `PUT /api/tournaments/[id]` - Update tournament
- `DELETE /api/tournaments/[id]` - Delete tournament

### 2. Registration Management
- `POST /api/tournaments/[id]/register` - Register for tournament
- `GET /api/tournaments/[id]/registrations` - Get registrations
- `PUT /api/registrations/[id]` - Update registration
- `DELETE /api/registrations/[id]` - Cancel registration

### 3. Match Management
- `GET /api/tournaments/[id]/matches` - Get tournament matches
- `POST /api/matches` - Create match
- `PUT /api/matches/[id]/score` - Update match score
- `PUT /api/matches/[id]/status` - Update match status

### 4. Live Display
- `GET /api/tournaments/[id]/live` - Get live tournament data
- `PUT /api/tournaments/[id]/display-settings` - Update display settings

## Environment Variables Required

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Implementation Steps

### Phase 1: Basic Setup
1. Create Supabase project
2. Set up database schema
3. Configure authentication
4. Implement basic CRUD operations

### Phase 2: User Management
1. Implement user registration/login
2. Set up role-based access control
3. Create user profile management

### Phase 3: Tournament Features
1. Tournament creation and management
2. Registration system
3. Match scheduling and scoring

### Phase 4: Real-time Features
1. Live match updates
2. Real-time registration updates
3. Live display synchronization

### Phase 5: Admin Features
1. IT admin dashboard integration
2. Tournament admin tools
3. System monitoring and logs

## Security Considerations

1. **Row Level Security**: Implement comprehensive RLS policies
2. **API Rate Limiting**: Implement rate limiting for API endpoints
3. **Input Validation**: Validate all user inputs on both client and server
4. **Audit Logging**: Log all administrative actions
5. **Data Encryption**: Ensure sensitive data is encrypted at rest
6. **CORS Configuration**: Properly configure CORS for production

## Testing Strategy

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test API endpoints and database operations
3. **E2E Tests**: Test complete user workflows
4. **Load Testing**: Test system performance under load
5. **Security Testing**: Test authentication and authorization

This plan provides a comprehensive roadmap for integrating Supabase into the gaming tournament platform while maintaining security, performance, and scalability.
