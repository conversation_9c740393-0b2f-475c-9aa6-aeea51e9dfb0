'use client';

import { motion } from 'framer-motion';
import { Shield, Eye, Lock, UserCheck, Database, Globe } from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Card } from '@/components/ui';

const privacySections = [
  {
    title: 'Information We Collect',
    icon: Database,
    content: [
      'Personal information (name, email, phone number) provided during tournament registration',
      'Gaming information (PSN ID, FIFA skill level, tournament preferences)',
      'Emergency contact information for in-person events',
      'Payment information for tournament fees (processed securely through third-party providers)',
      'Website usage data and analytics to improve our services'
    ]
  },
  {
    title: 'How We Use Your Information',
    icon: UserCheck,
    content: [
      'Tournament registration and management',
      'Communication about tournament schedules, updates, and results',
      'Emergency contact purposes during in-person events',
      'Improving our services and user experience',
      'Legal compliance and fraud prevention',
      'Marketing communications (with your consent)'
    ]
  },
  {
    title: 'Information Sharing',
    icon: Globe,
    content: [
      'We do not sell, trade, or rent your personal information to third parties',
      'Tournament results and leaderboards may be publicly displayed (gamertags only)',
      'Venue partners receive necessary information for event management',
      'Legal authorities if required by law or to protect our rights',
      'Service providers who assist in tournament operations (under strict confidentiality)'
    ]
  },
  {
    title: 'Data Security',
    icon: Lock,
    content: [
      'Industry-standard encryption for data transmission and storage',
      'Secure servers with regular security updates and monitoring',
      'Limited access to personal information on a need-to-know basis',
      'Regular security audits and vulnerability assessments',
      'Secure payment processing through certified third-party providers'
    ]
  },
  {
    title: 'Your Rights',
    icon: Eye,
    content: [
      'Access your personal information we have on file',
      'Request correction of inaccurate or incomplete information',
      'Request deletion of your personal information (subject to legal requirements)',
      'Opt-out of marketing communications at any time',
      'Data portability - receive your information in a structured format',
      'Lodge complaints with relevant data protection authorities'
    ]
  }
];

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Shield className="h-16 w-16 text-blue-400 mx-auto mb-4" />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Privacy Policy
          </h1>
          <p className="text-xl text-gray-300">
            Your privacy is important to us. Learn how we protect your information.
          </p>
          <p className="text-gray-400 mt-2">
            Last updated: January 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Card className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Introduction</h2>
            <p className="text-gray-300 mb-4">
              Ethiopia Gaming Events ("we," "our," or "us") is committed to protecting your privacy. 
              This Privacy Policy explains how we collect, use, disclose, and safeguard your information 
              when you use our gaming tournament platform and services.
            </p>
            <p className="text-gray-300">
              By using our services, you agree to the collection and use of information in accordance 
              with this policy. If you do not agree with our policies and practices, do not use our services.
            </p>
          </Card>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-6">
          {privacySections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * (index + 2) }}
            >
              <Card className="p-6">
                <div className="flex items-center mb-4">
                  <section.icon className="h-6 w-6 text-blue-400 mr-3" />
                  <h2 className="text-xl font-bold text-white">{section.title}</h2>
                </div>
                <ul className="space-y-2">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="text-gray-300 flex items-start">
                      <span className="text-blue-400 mr-2 mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-8 space-y-6"
        >
          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Cookies and Tracking</h2>
            <p className="text-gray-300 mb-3">
              We use cookies and similar tracking technologies to enhance your experience on our website:
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Essential cookies for website functionality</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Analytics cookies to understand website usage</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Preference cookies to remember your settings</span>
              </li>
            </ul>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Data Retention</h2>
            <p className="text-gray-300 mb-3">
              We retain your personal information for as long as necessary to:
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Provide our services and maintain tournament records</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Comply with legal obligations and resolve disputes</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Maintain historical tournament data for statistical purposes</span>
              </li>
            </ul>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Children's Privacy</h2>
            <p className="text-gray-300">
              Our services are not intended for children under 16 years of age. We do not knowingly 
              collect personal information from children under 16. Players under 18 require parental 
              consent and must provide emergency contact information.
            </p>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Changes to This Policy</h2>
            <p className="text-gray-300 mb-3">
              We may update this Privacy Policy from time to time. We will notify you of any changes by:
            </p>
            <ul className="space-y-2 text-gray-300 mb-4">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Posting the new Privacy Policy on this page</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Sending email notifications for significant changes</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2 mt-1">•</span>
                <span>Updating the "Last updated" date at the top of this policy</span>
              </li>
            </ul>
            <p className="text-gray-300">
              Your continued use of our services after any changes constitutes acceptance of the new policy.
            </p>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Contact Us</h2>
            <p className="text-gray-300 mb-4">
              If you have any questions about this Privacy Policy or our data practices, please contact us:
            </p>
            <div className="space-y-2 text-gray-300">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> +251-11-XXX-XXXX</p>
              <p><strong>Address:</strong> Bole Road, Addis Ababa, Ethiopia</p>
            </div>
          </Card>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
