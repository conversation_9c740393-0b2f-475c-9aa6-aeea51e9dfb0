# Gaming Tournament Platform - Deployment Guide

## Overview
This guide covers the complete deployment process for the Gaming Tournament Platform, from development to production.

## Development Environment Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Local Development
1. Clone the repository
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`
4. Access at `http://localhost:3000`

## Production Deployment Options

### Option 1: Vercel (Recommended)

#### Advantages
- Seamless Next.js integration
- Automatic deployments from Git
- Built-in CDN and edge functions
- Zero-config deployment

#### Steps
1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy from project directory
   vercel
   ```

2. **Environment Variables**
   Set in Vercel dashboard:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_key
   ```

3. **Custom Domain**
   - Add domain in Vercel dashboard
   - Configure DNS records
   - SSL automatically handled

### Option 2: Netlify

#### Steps
1. **Build Settings**
   ```
   Build command: npm run build
   Publish directory: out
   ```

2. **Next.js Configuration**
   ```javascript
   // next.config.js
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     output: 'export',
     trailingSlash: true,
     images: {
       unoptimized: true
     }
   }
   
   module.exports = nextConfig
   ```

### Option 3: Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  gaming-tournament:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_KEY}
    restart: unless-stopped
```

### Option 4: AWS Deployment

#### Using AWS Amplify
1. Connect GitHub repository
2. Configure build settings
3. Set environment variables
4. Deploy automatically on push

#### Using EC2 + PM2
```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
# ecosystem.config.js
module.exports = {
  apps: [{
    name: 'gaming-tournament',
    script: 'npm',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Supabase Setup for Production

### 1. Create Production Project
1. Go to Supabase dashboard
2. Create new project
3. Choose production tier
4. Configure database settings

### 2. Database Migration
```sql
-- Run all schema creation scripts
-- Set up RLS policies
-- Create initial data
```

### 3. Authentication Configuration
- Configure email templates
- Set up OAuth providers
- Configure JWT settings
- Set up custom domains

### 4. API Configuration
- Configure CORS settings
- Set up rate limiting
- Configure webhooks

## Environment Configuration

### Production Environment Variables
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# Optional: Analytics
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

## Performance Optimization

### 1. Image Optimization
```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['your-supabase-project.supabase.co'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

### 2. Bundle Analysis
```bash
# Install bundle analyzer
npm install --save-dev @next/bundle-analyzer

# Analyze bundle
npm run analyze
```

### 3. Caching Strategy
- Static assets: Long-term caching
- API responses: Short-term caching
- Database queries: Implement query caching

## Monitoring and Analytics

### 1. Error Tracking
```bash
# Install Sentry
npm install @sentry/nextjs

# Configure in next.config.js
const { withSentryConfig } = require('@sentry/nextjs');
```

### 2. Performance Monitoring
- Web Vitals tracking
- Real User Monitoring (RUM)
- Server-side performance metrics

### 3. Uptime Monitoring
- Set up health check endpoints
- Configure alerting
- Monitor database connectivity

## Security Checklist

### Pre-deployment
- [ ] Environment variables secured
- [ ] API keys rotated
- [ ] HTTPS enforced
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation in place
- [ ] SQL injection prevention
- [ ] XSS protection enabled

### Post-deployment
- [ ] Security headers configured
- [ ] SSL certificate valid
- [ ] Database backups enabled
- [ ] Audit logging active
- [ ] Access controls tested
- [ ] Vulnerability scanning completed

## Backup and Recovery

### 1. Database Backups
- Automated daily backups
- Point-in-time recovery
- Cross-region replication

### 2. Application Backups
- Source code in version control
- Environment configuration documented
- Deployment scripts versioned

### 3. Recovery Procedures
- Database restoration process
- Application rollback procedure
- Disaster recovery plan

## CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Build application
        run: npm run build
        
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## Post-Deployment Checklist

### Immediate (0-24 hours)
- [ ] Application loads correctly
- [ ] All pages accessible
- [ ] Authentication working
- [ ] Database connections stable
- [ ] Real-time features functional
- [ ] Admin dashboards accessible

### Short-term (1-7 days)
- [ ] Performance metrics baseline
- [ ] Error rates within acceptable limits
- [ ] User feedback collected
- [ ] Load testing completed
- [ ] Security scan results reviewed

### Long-term (1-4 weeks)
- [ ] User adoption metrics
- [ ] Performance optimization opportunities
- [ ] Feature usage analytics
- [ ] Scaling requirements assessed
- [ ] Backup and recovery tested

## Troubleshooting Common Issues

### Build Failures
- Check Node.js version compatibility
- Verify all dependencies installed
- Review build logs for specific errors

### Runtime Errors
- Check environment variables
- Verify database connectivity
- Review application logs

### Performance Issues
- Analyze bundle size
- Check database query performance
- Review caching configuration

This deployment guide ensures a smooth transition from development to production while maintaining security, performance, and reliability standards.
