'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Users, Clock, Star } from 'lucide-react';

// Mock tournament data
const tournamentData = {
  title: 'FIFA 24 Championship Finals',
  game: 'FIFA 24',
  platform: 'PlayStation 5',
  round: 'Grand Finals',
  bestOf: 3,
  currentMatch: 2,
  venue: 'Addis Gaming Arena',
  teams: [
    {
      name: 'FifaKing_23',
      logo: '👑',
      score: 1,
      realName: '<PERSON> Rodriguez',
      division: 'Elite Division',
      color: 'from-blue-500 to-blue-700',
    },
    {
      name: 'GoalMachine',
      logo: '⚽',
      score: 1,
      realName: '<PERSON> Smith',
      division: 'Division 1',
      color: 'from-green-500 to-green-700',
    },
  ],
  spectators: 89,
  prizePool: '$5,000',
  currentScore: '2-1',
  matchTime: '67\'',
};

export default function DisplayPage() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isLive, setIsLive] = useState(true);
  const [scoreAnimation, setScoreAnimation] = useState<'team1' | 'team2' | null>(null);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate score updates for demonstration
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.8) {
        const team = Math.random() > 0.5 ? 'team1' : 'team2';
        setScoreAnimation(team);
        setTimeout(() => setScoreAnimation(null), 2000);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-green-500/10 to-blue-500/10 animate-pulse" />
      </div>

      {/* Header */}
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10 bg-black/50 backdrop-blur-sm border-b border-gray-700"
      >
        <div className="max-w-7xl mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <Trophy className="h-8 w-8 text-yellow-400" />
                <div>
                  <h1 className="text-2xl font-bold text-white">{tournamentData.title}</h1>
                  <p className="text-gray-300">{tournamentData.game}</p>
                </div>
              </div>
              
              {isLive && (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                  <span className="text-red-400 font-semibold text-lg">LIVE</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-8 text-gray-300">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span className="text-lg">{tournamentData.spectators} spectators</span>
              </div>
              <div className="flex items-center space-x-2">
                <Trophy className="h-5 w-5" />
                <span className="text-lg">{tournamentData.prizePool}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-lg">{tournamentData.platform}</span>
              </div>
              <div className="text-lg">
                {currentTime.toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-6xl">
          {/* Match Info */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">{tournamentData.round}</h2>
            <div className="flex items-center justify-center space-x-4 text-gray-300">
              <span className="text-xl">Best of {tournamentData.bestOf}</span>
              <span className="text-gray-500">•</span>
              <span className="text-xl">Match {tournamentData.currentMatch}/{tournamentData.bestOf}</span>
              <span className="text-gray-500">•</span>
              <span className="text-xl">{tournamentData.venue}</span>
            </div>
          </motion.div>

          {/* Teams and Score */}
          <div className="grid grid-cols-3 gap-8 items-center">
            {/* Team 1 */}
            <motion.div
              initial={{ x: -200, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 1, delay: 0.5 }}
              className="text-right"
            >
              <div className={`bg-gradient-to-r ${tournamentData.teams[0].color} p-8 rounded-2xl shadow-2xl`}>
                <div className="flex items-center justify-end space-x-4 mb-4">
                  <div>
                    <h3 className="text-2xl font-bold text-white">{tournamentData.teams[0].name}</h3>
                    <p className="text-lg text-gray-200">{tournamentData.teams[0].realName}</p>
                    <div className="flex justify-end mt-2">
                      <span className="text-sm text-gray-200 bg-black/20 px-3 py-1 rounded-full">
                        {tournamentData.teams[0].division}
                      </span>
                    </div>
                  </div>
                  <div className="text-6xl">{tournamentData.teams[0].logo}</div>
                </div>
              </div>
            </motion.div>

            {/* Score */}
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-center"
            >
              <div className="bg-black/50 backdrop-blur-sm rounded-3xl p-8 border border-gray-700">
                <div className="flex items-center justify-center space-x-8">
                  <AnimatePresence>
                    <motion.div
                      key={tournamentData.teams[0].score}
                      initial={{ scale: scoreAnimation === 'team1' ? 1.5 : 1 }}
                      animate={{ scale: 1 }}
                      className={`text-8xl font-bold ${scoreAnimation === 'team1' ? 'text-blue-400' : 'text-white'}`}
                    >
                      {tournamentData.teams[0].score}
                    </motion.div>
                  </AnimatePresence>
                  
                  <div className="text-4xl text-gray-500 font-bold">:</div>
                  
                  <AnimatePresence>
                    <motion.div
                      key={tournamentData.teams[1].score}
                      initial={{ scale: scoreAnimation === 'team2' ? 1.5 : 1 }}
                      animate={{ scale: 1 }}
                      className={`text-8xl font-bold ${scoreAnimation === 'team2' ? 'text-red-400' : 'text-white'}`}
                    >
                      {tournamentData.teams[1].score}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>

            {/* Team 2 */}
            <motion.div
              initial={{ x: 200, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 1, delay: 0.5 }}
              className="text-left"
            >
              <div className={`bg-gradient-to-l ${tournamentData.teams[1].color} p-8 rounded-2xl shadow-2xl`}>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="text-6xl">{tournamentData.teams[1].logo}</div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">{tournamentData.teams[1].name}</h3>
                    <p className="text-lg text-gray-200">{tournamentData.teams[1].realName}</p>
                    <div className="flex mt-2">
                      <span className="text-sm text-gray-200 bg-black/20 px-3 py-1 rounded-full">
                        {tournamentData.teams[1].division}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom Stats */}
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="mt-12 grid grid-cols-3 gap-8"
          >
            <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 text-center border border-gray-700">
              <Clock className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{tournamentData.matchTime}</div>
              <div className="text-gray-400">Match Time</div>
            </div>

            <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 text-center border border-gray-700">
              <Star className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{tournamentData.currentScore}</div>
              <div className="text-gray-400">Current Score</div>
            </div>

            <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 text-center border border-gray-700">
              <Trophy className="h-8 w-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{tournamentData.venue}</div>
              <div className="text-gray-400">Venue</div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Floating Particles Animation */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400 rounded-full opacity-20"
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>
    </div>
  );
}
