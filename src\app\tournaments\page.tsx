'use client';

import { motion } from 'framer-motion';
import { Calendar, Users, Trophy, Clock, MapPin, Gamepad2 } from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { <PERSON><PERSON>, Card, Badge } from '@/components/ui';

const tournaments = [
  {
    id: 1,
    title: 'FIFA 24 Championship Finals',
    game: 'FIFA 24',
    platform: 'PS5',
    status: 'Live',
    participants: 32,
    prizePool: '$5,000',
    startDate: '2024-01-15',
    location: 'Addis Gaming Arena',
    address: 'Bole Road, Addis Ababa, Ethiopia',
    registrationDeadline: '2024-01-10',
    description: 'The ultimate FIFA 24 championship featuring Ethiopia\'s best players competing for the grand prize.',
  },
  {
    id: 2,
    title: 'FIFA Winter Cup',
    game: 'FIFA 24',
    platform: 'PS5',
    status: 'Registration Open',
    participants: 16,
    prizePool: '$2,500',
    startDate: '2024-02-01',
    location: 'Merkato Gaming Hub',
    address: 'Merkato District, Addis Ababa, Ethiopia',
    registrationDeadline: '2024-01-25',
    description: 'Seasonal tournament perfect for intermediate players looking to test their skills.',
  },
  {
    id: 3,
    title: 'FIFA Rookie League',
    game: 'FIFA 24',
    platform: 'PS5',
    status: 'Registration Open',
    participants: 24,
    prizePool: '$1,500',
    startDate: '2024-02-15',
    location: 'Piazza Gaming Center',
    address: 'Piazza Area, Addis Ababa, Ethiopia',
    registrationDeadline: '2024-02-08',
    description: 'Perfect entry-level tournament for newcomers to competitive FIFA gaming.',
  },
  {
    id: 4,
    title: 'FIFA Masters Cup',
    game: 'FIFA 24',
    platform: 'PS5',
    status: 'Upcoming',
    participants: 48,
    prizePool: '$7,500',
    startDate: '2024-03-01',
    location: 'Kazanchis Pro Lounge',
    address: 'Kazanchis, Addis Ababa, Ethiopia',
    registrationDeadline: '2024-02-20',
    description: 'Elite tournament for experienced players with the highest prize pool of the season.',
  },
  {
    id: 5,
    title: 'FIFA New Year Classic',
    game: 'FIFA 24',
    platform: 'PS5',
    status: 'Completed',
    participants: 32,
    prizePool: '$4,000',
    startDate: '2024-01-01',
    location: 'Addis Gaming Arena',
    address: 'Bole Road, Addis Ababa, Ethiopia',
    registrationDeadline: '2023-12-25',
    description: 'Kicked off the new year with an exciting tournament that saw amazing gameplay.',
  },
];

const statusColors = {
  'Live': 'bg-red-500 text-white',
  'Upcoming': 'bg-blue-500 text-white',
  'Registration Open': 'bg-green-500 text-white',
  'Completed': 'bg-gray-500 text-white',
};

export default function TournamentsPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            FIFA 24 Tournaments
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Join in-person FIFA 24 competitions on PS5 at premium gaming venues across Addis Ababa, Ethiopia
          </p>
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            <Badge variant="success" className="text-sm px-4 py-2">
              <Gamepad2 className="h-4 w-4 mr-2" />
              PS5 Exclusive
            </Badge>
            <Badge variant="info" className="text-sm px-4 py-2">
              <MapPin className="h-4 w-4 mr-2" />
              In-Person Only
            </Badge>
            <Badge variant="warning" className="text-sm px-4 py-2">
              <Trophy className="h-4 w-4 mr-2" />
              Cash Prizes
            </Badge>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/register">
              <Button size="lg">
                <Trophy className="mr-2 h-5 w-5" />
                Register for Tournament
              </Button>
            </a>
            <a href="/leaderboard">
              <Button variant="secondary" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                View Leaderboard
              </Button>
            </a>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {tournaments.map((tournament, index) => (
            <motion.div
              key={tournament.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card hover className="overflow-hidden">
                <div className="relative">
                  <div className="h-40 bg-gradient-to-r from-green-600 to-blue-600 flex items-center justify-center">
                    <div className="text-center">
                      <Gamepad2 className="h-12 w-12 text-white mx-auto mb-2" />
                      <span className="text-white font-semibold">{tournament.platform}</span>
                    </div>
                  </div>
                  <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-semibold ${statusColors[tournament.status as keyof typeof statusColors]}`}>
                    {tournament.status}
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">
                    {tournament.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-4">{tournament.description}</p>

                  <div className="space-y-2 mb-6">
                    <div className="flex items-center text-gray-300 text-sm">
                      <Users className="h-4 w-4 mr-2 text-blue-400" />
                      <span>{tournament.participants} participants</span>
                    </div>
                    <div className="flex items-center text-gray-300 text-sm">
                      <Trophy className="h-4 w-4 mr-2 text-yellow-400" />
                      <span>{tournament.prizePool} prize pool</span>
                    </div>
                    <div className="flex items-center text-gray-300 text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-green-400" />
                      <span>{new Date(tournament.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center text-gray-300 text-sm">
                      <MapPin className="h-4 w-4 mr-2 text-red-400" />
                      <span>{tournament.location}</span>
                    </div>
                    <div className="flex items-center text-gray-300 text-sm">
                      <Clock className="h-4 w-4 mr-2 text-purple-400" />
                      <span>Register by {new Date(tournament.registrationDeadline).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <a href="/rules">
                      <Button className="flex-1" size="sm">
                        View Details
                      </Button>
                    </a>
                    {tournament.status === 'Registration Open' && (
                      <a href="/register">
                        <Button variant="secondary" size="sm">
                          Register
                        </Button>
                      </a>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      <Footer />
    </div>
  );
}
