'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  Building,
  Globe,
  MessageCircle
} from 'lucide-react';
import { Navigation, Footer } from '@/components/layout';
import { Card, Button, Input } from '@/components/ui';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    inquiryType: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    // Handle form submission
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <MessageCircle className="h-16 w-16 text-blue-400 mx-auto mb-4" />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Contact Us
          </h1>
          <p className="text-xl text-gray-300">
            Get in touch with Ethiopia&apos;s premier gaming tournament organizers
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-6">
              <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Full Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Your full name"
                    required
                  />
                  <Input
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-300">
                    Inquiry Type
                  </label>
                  <select
                    name="inquiryType"
                    value={formData.inquiryType}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select inquiry type</option>
                    <option value="tournament">Tournament Information</option>
                    <option value="registration">Registration Support</option>
                    <option value="venue">Venue Information</option>
                    <option value="partnership">Partnership Opportunities</option>
                    <option value="technical">Technical Support</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <Input
                  label="Subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="Brief subject of your message"
                  required
                />

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-300">
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell us how we can help you..."
                    required
                  />
                </div>

                <Button type="submit" size="lg" className="w-full">
                  <Send className="mr-2 h-5 w-5" />
                  Send Message
                </Button>
              </form>
            </Card>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Company Info */}
            <Card className="p-6">
              <h3 className="text-xl font-bold text-white mb-4">Ethiopia Gaming Events</h3>
              <p className="text-gray-300 mb-4">
                Ethiopia&apos;s leading gaming tournament organizer, bringing competitive FIFA gaming
                to players across Addis Ababa and beyond.
              </p>
              <div className="space-y-3">
                <div className="flex items-center text-gray-300">
                  <Building className="h-5 w-5 mr-3 text-blue-400" />
                  <span>Established 2023</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Globe className="h-5 w-5 mr-3 text-green-400" />
                  <span>Serving all of Ethiopia</span>
                </div>
              </div>
            </Card>

            {/* Contact Details */}
            <Card className="p-6">
              <h3 className="text-xl font-bold text-white mb-4">Get in Touch</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Phone className="h-5 w-5 text-blue-400 mt-1" />
                  <div>
                    <p className="text-white font-semibold">Phone</p>
                    <p className="text-gray-300">+251-11-XXX-XXXX</p>
                    <p className="text-gray-400 text-sm">Mon-Fri 9AM-6PM EAT</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Mail className="h-5 w-5 text-green-400 mt-1" />
                  <div>
                    <p className="text-white font-semibold">Email</p>
                    <p className="text-gray-300"><EMAIL></p>
                    <p className="text-gray-300"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-red-400 mt-1" />
                  <div>
                    <p className="text-white font-semibold">Office Location</p>
                    <p className="text-gray-300">Bole Road, Addis Ababa</p>
                    <p className="text-gray-300">Ethiopia</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-purple-400 mt-1" />
                  <div>
                    <p className="text-white font-semibold">Business Hours</p>
                    <p className="text-gray-300">Monday - Friday: 9:00 AM - 6:00 PM</p>
                    <p className="text-gray-300">Saturday: 10:00 AM - 4:00 PM</p>
                    <p className="text-gray-300">Sunday: Closed</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Tournament Venues */}
            <Card className="p-6">
              <h3 className="text-xl font-bold text-white mb-4">Tournament Venues</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-700 rounded-lg">
                  <p className="text-white font-semibold">Addis Gaming Arena</p>
                  <p className="text-gray-400 text-sm">Bole Road, Addis Ababa</p>
                </div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <p className="text-white font-semibold">Merkato Gaming Hub</p>
                  <p className="text-gray-400 text-sm">Merkato District, Addis Ababa</p>
                </div>
                <div className="p-3 bg-gray-700 rounded-lg">
                  <p className="text-white font-semibold">Piazza Gaming Center</p>
                  <p className="text-gray-400 text-sm">Piazza Area, Addis Ababa</p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Map Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-12"
        >
          <Card className="p-6">
            <h3 className="text-xl font-bold text-white mb-4">Find Us</h3>
            <div className="bg-gray-700 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center">
                <MapPin className="h-12 w-12 text-blue-400 mx-auto mb-2" />
                <p className="text-gray-300">Interactive map coming soon</p>
                <p className="text-gray-400 text-sm">Bole Road, Addis Ababa, Ethiopia</p>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
